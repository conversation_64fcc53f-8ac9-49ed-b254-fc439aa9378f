#include "DruidsSageChatView.h"

#include <Dom/JsonObject.h>
#include <Serialization/JsonWriter.h>
#include <Serialization/JsonReader.h>
#include <Serialization/JsonSerializer.h>
#include <Misc/FileHelper.h>
#include <HAL/FileManager.h>

#include <Widgets/Layout/SScrollBox.h>
#include <Framework/Application/SlateApplication.h>
#include <Input/Events.h>
#include <Engine/Engine.h>
#include <Engine/World.h>
#include "TimerManager.h"
#include <Components/PanelWidget.h>
#include <Components/Button.h>

#include "LogDruids.h"
#include "DruidsSageHelper.h"
#include "IChatRequestHandler.h"
#include "ChatItems/DruidsSageSimpleChatItem.h"
#include "ChatItems/DruidsSageAssistantChatItem.h"
#include "ISageExtensionDelegator.h"
#include "DruidsSageMultiLineTextInput.h"
#include "ContextChipWidget.h"
#include "Components/VerticalBox.h"
#include "Components/ScrollBox.h"
#include "SageUIModule.h"
#include "ChatWidgetOverrides.h"

UDruidsSageChatView::UDruidsSageChatView(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
	, BPContextChipWidget(nullptr)
	, TabContextChipWidget(nullptr)
	, InputTextBox(nullptr)
	, SendButton(nullptr)
	, ClearButton(nullptr)
	, ChatScrollBox()
	, ChatBox()
	, CurrentTabContext(TEXT(""))
	, CurrentTabContextDisplayMessage(TEXT(""))
	, CurrentBPContext(TEXT(""))
	, CurrentBPContextDisplayMessage(TEXT(""))
{
}

void UDruidsSageChatView::NativePreConstruct()
{
	Super::NativePreConstruct();
}

void UDruidsSageChatView::NativeConstruct()
{
	Super::NativeConstruct();

	if (IsDesignTime())
	{
		return;
	}

	// Initialize chat content area
	// Chat content is now handled by UMG widgets bound in Blueprint

	// Bind the Enter key event from the input text box
	if (InputTextBox)
	{
		InputTextBox->OnEnterPressed.AddDynamic(this, &UDruidsSageChatView::HandleEnterPressed);
	}

	// Bind button click events
	if (SendButton)
	{
		SendButton->OnClicked.AddDynamic(this, &UDruidsSageChatView::HandleSendButtonClicked);
	}

	if (ClearButton)
	{
		ClearButton->OnClicked.AddDynamic(this, &UDruidsSageChatView::HandleClearButtonClicked);
	}

	// Load chat history
	LoadChatHistory();

	// Set focus to the input text box after a short delay to ensure window is fully constructed
	if (GWorld)
	{
		GWorld->GetTimerManager().SetTimerForNextTick([this]()
		{
			SetInputFocus();
		});
	}
}

void UDruidsSageChatView::NativeDestruct()
{
	Super::NativeDestruct();

	if (IsDesignTime())
	{
		return;
	}

	SaveChatHistory();
	ClearChat();
}


void UDruidsSageChatView::SynchronizeProperties()
{
	Super::SynchronizeProperties();
}

void UDruidsSageChatView::ReleaseSlateResources(bool bReleaseChildren)
{
	Super::ReleaseSlateResources(bReleaseChildren);

	ChatItems.Empty();

	OnMessageSending.Clear();
}

bool UDruidsSageChatView::IsSendMessageEnabled() const
{
	const bool bNoActiveRequest = ChatRequestHandler.IsValid() && ChatRequestHandler.Get()->IsNoActiveRequest();
	return bNoActiveRequest && InputTextBox && !InputTextBox->IsEmpty();
}

bool UDruidsSageChatView::IsClearChatEnabled() const
{
	return !ChatItems.IsEmpty();
}

void UDruidsSageChatView::ClearChat()
{
	// First stop any ongoing request
	if (ChatRequestHandler.IsValid())
	{
		ChatRequestHandler.Get()->StopAndCleanupRequest(ChatItems);
	}

	// Clear all chat items
	ChatItems.Empty();

	// Clear the chat box if it exists
	if (ChatBox)
	{
		ChatBox->ClearChildren();
	}

	// Update context chip visibility
	if (BPContextChipWidget)
	{
		BPContextChipWidget->SetVisibility(CurrentBPContextDisplayMessage.IsEmpty() ? ESlateVisibility::Collapsed : ESlateVisibility::Visible);
	}
}

void UDruidsSageChatView::SetTabContext(const FString& Context, const FString& ContextDisplayMessage)
{
	CurrentTabContext = Context;
	CurrentTabContextDisplayMessage = ContextDisplayMessage;

	FString TruncatedMessage = TruncateString(ContextDisplayMessage);

	if (TabContextChipWidget)
	{
		TabContextChipWidget->SetChipText(TruncatedMessage);
		TabContextChipWidget->SetVisibility(TruncatedMessage.IsEmpty() ? ESlateVisibility::Collapsed : ESlateVisibility::Visible);
	}
}

void UDruidsSageChatView::SetBPContext(const FString& BPContext, const FString& BPContextDisplayMessage)
{
	CurrentBPContext = BPContext;
	CurrentBPContextDisplayMessage = BPContextDisplayMessage;

	FString TruncatedMessage = TruncateString(BPContextDisplayMessage);

	if (BPContextChipWidget)
	{
		BPContextChipWidget->SetChipText(TruncatedMessage);
		BPContextChipWidget->SetVisibility(TruncatedMessage.IsEmpty() ? ESlateVisibility::Collapsed : ESlateVisibility::Visible);
	}
}

void UDruidsSageChatView::SetActiveObject(const TWeakObjectPtr<>& NewActiveObject)
{
	ActiveObject = NewActiveObject;
	UE_LOG(LogDruidsSage_Internal, Display, TEXT("Active Object Set: %s"), *GetNameSafe(NewActiveObject.Get()));
}

void UDruidsSageChatView::SetActiveExtensionDefinitions(
	const TArray<TSharedPtr<FDruidsSageExtensionDefinition>>& NewActiveExtensions)
{
	ActiveExtensionDefinitions = NewActiveExtensions;
}

void UDruidsSageChatView::SetChatRequestHandler(const TWeakPtr<IChatRequestHandler>& NewChatRequestHandler)
{
	ChatRequestHandler = NewChatRequestHandler.Pin().ToSharedRef();
}

void UDruidsSageChatView::SetExtensionsDelegator(const TWeakPtr<ISageExtensionDelegator>& NewExtensionDelegator)
{
	ExtensionDelegator = NewExtensionDelegator.Pin();
}

void UDruidsSageChatView::SetInputFocus() const
{
	if (InputTextBox)
	{
		InputTextBox->SetInputFocus();
	}
}

void UDruidsSageChatView::HandleEnterPressed()
{
	if (IsSendMessageEnabled())
	{
		HandleSendMessage(EDruidsSageChatRole::User);
	}
}

void UDruidsSageChatView::HandleSendButtonClicked()
{
	if (IsSendMessageEnabled())
	{
		HandleSendMessage(EDruidsSageChatRole::User);
	}
}

void UDruidsSageChatView::HandleClearButtonClicked()
{
	if (IsClearChatEnabled())
	{
		HandleClearChat();
	}
}

FString UDruidsSageChatView::TruncateString(const FString& Input, int32 MaxLength /*= 100*/)
{
	if (Input.Len() > MaxLength)
	{
		return Input.Left(MaxLength - 3) + TEXT("...");
	}
	return Input;
}

UIDruidsSageChatItem* UDruidsSageChatView::CreateChatItem(
	EDruidsSageChatRole Role,
	const FString& ChatText) const
{
	if (Role == EDruidsSageChatRole::Assistant)
	{
		// Get the widget class from overrides
		TSubclassOf<UDruidsSageAssistantChatItem> WidgetClass = UDruidsSageAssistantChatItem::StaticClass();
		if (FSageUIModule* SageUIModule = FModuleManager::GetModulePtr<FSageUIModule>("SageUI"))
		{
			if (UChatWidgetOverrides* Overrides = SageUIModule->GetChatWidgetOverrides())
			{
				if (TSubclassOf<UDruidsSageAssistantChatItem> OverrideClass = Overrides->GetAssistantChatItemWidgetClass())
				{
					WidgetClass = OverrideClass;
				}
			}
		}

		UDruidsSageAssistantChatItem* AssistantChatItem = CreateWidget<UDruidsSageAssistantChatItem>(GetWorld(), WidgetClass);
		if (AssistantChatItem)
		{
			AssistantChatItem->InitializeAssistantChatItem();
			AssistantChatItem->SetScrollBoxReference(ChatScrollBox);
			AssistantChatItem->OnActionApplied.AddDynamic(const_cast<UDruidsSageChatView*>(this), &UDruidsSageChatView::OnActionRequestApplied);
			AssistantChatItem->SetRawText(ChatText);
		}
		return AssistantChatItem;
	}

	// Get the widget class from overrides
	TSubclassOf<UDruidsSageSimpleChatItem> WidgetClass = UDruidsSageSimpleChatItem::StaticClass();
	if (FSageUIModule* SageUIModule = FModuleManager::GetModulePtr<FSageUIModule>("SageUI"))
	{
		if (UChatWidgetOverrides* Overrides = SageUIModule->GetChatWidgetOverrides())
		{
			if (TSubclassOf<UDruidsSageSimpleChatItem> OverrideClass = Overrides->GetSimpleChatItemWidgetClass())
			{
				WidgetClass = OverrideClass;
			}
		}
	}

	UDruidsSageSimpleChatItem* SimpleChatItem = CreateWidget<UDruidsSageSimpleChatItem>(GetWorld(), WidgetClass);
	if (SimpleChatItem)
	{
		SimpleChatItem->InitializeSimpleChatItem(Role, ChatText, ActiveExtensionDefinitions);
		SimpleChatItem->SetScrollBoxReference(ChatScrollBox);
	}
	return SimpleChatItem;
}

void UDruidsSageChatView::OnActionRequestApplied(const FString& ActionDetailsJson)
{
	if (ExtensionDelegator.IsValid())
	{
		// Convert string back to TSharedPtr<FJsonValue> for the delegator
		TSharedPtr<FJsonValue> JsonValue;
		TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(ActionDetailsJson);
		if (FJsonSerializer::Deserialize(Reader, JsonValue))
		{
			ExtensionDelegator.Get()->OnActionApplied(JsonValue);
		}
	}
}

void UDruidsSageChatView::HandleSendMessage(const EDruidsSageChatRole Role)
{
	// Broadcast that we're about to send a message
	OnMessageSending.Broadcast();

	FString ChatText = InputTextBox ? InputTextBox->GetText().ToString() : FString();
	UIDruidsSageChatItem* NewUserChatItem = CreateChatItem(Role, ChatText);

	// Add to chat box if available
	if (ChatBox && NewUserChatItem)
	{
		ChatBox->AddChild(NewUserChatItem);
	}
	if (NewUserChatItem)
	{
		ChatItems.Add(NewUserChatItem);
	}

	if (Role == EDruidsSageChatRole::System)
	{
		if (ChatScrollBox)
		{
			ChatScrollBox->ScrollToEnd();
		}
		if (InputTextBox)
		{
			InputTextBox->SetText(FText::GetEmpty());
		}
		return;
	}

	UIDruidsSageChatItem* AssistantMessage = CreateChatItem(
		EDruidsSageChatRole::Assistant,
		FString()
	);

	// *** Combine Contexts for the AI Request ***
	// Simple concatenation for now. Might need more sophisticated formatting later.
	FString CombinedContext = "";
	if (!CurrentTabContext.IsEmpty())
	{
		CombinedContext += CurrentTabContext;
	}
	if (!CurrentBPContext.IsEmpty())
	{
		if (!CombinedContext.IsEmpty())
		{
			CombinedContext += "\n\n"; // Separator
		}
		CombinedContext += "Blueprint Context (current BP nodes selected):\n" + CurrentBPContext;
	}
	// ********************************************

	if (ChatRequestHandler.IsValid() && AssistantMessage)
	{
		ChatRequestHandler.Get()->SetupAndSendRequest(ChatItems, AssistantMessage, CombinedContext);
	}

	// Add to chat box if available
	if (ChatBox && AssistantMessage)
	{
		ChatBox->AddChild(AssistantMessage);
	}
	if (AssistantMessage)
	{
		ChatItems.Add(AssistantMessage);
	}

	if (ChatScrollBox)
	{
		ChatScrollBox->ScrollToEnd();
	}
	if (InputTextBox)
	{
		InputTextBox->SetText(FText::GetEmpty());
	}
}

void UDruidsSageChatView::HandleClearChat()
{
	ClearChat();
}

TArray<FDruidsSageChatMessage> UDruidsSageChatView::GetChatHistory() const
{
	TArray<FDruidsSageChatMessage> Output;

	for (UIDruidsSageChatItem* Item : ChatItems)
	{
		if (Item)
		{
			FDruidsSageChatMessage DruidsMessage;
			Item->FillInDruidsMessage(DruidsMessage);
			Output.Add(DruidsMessage);
		}
	}

	return Output;
}

FString UDruidsSageChatView::GetHistoryPath()
{
	return FPaths::Combine(FPaths::ProjectSavedDir(), TEXT("DruidsSage"), TEXT("ChatHistory.json"));
}

void UDruidsSageChatView::LoadChatHistory()
{
	ChatItems.Empty();
	if (ChatBox)
	{
		ChatBox->ClearChildren();
	}

	SetTabContext(TEXT(""), TEXT(""));
	SetBPContext(TEXT(""), TEXT(""));

	if (const FString LoadPath = GetHistoryPath(); FPaths::FileExists(LoadPath))
	{
		FString FileContent;
		if (!FFileHelper::LoadFileToString(FileContent, *LoadPath))
		{
			return;
		}

		const TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(FileContent);
		TSharedPtr<FJsonObject> JsonObject;

		if (FJsonSerializer::Deserialize(Reader, JsonObject) && JsonObject.IsValid())
		{
			const TArray<TSharedPtr<FJsonValue>>* DataArray;
			if (JsonObject->TryGetArrayField(TEXT("Data"), DataArray))
			{
				for (const TSharedPtr<FJsonValue>& MessageValue : *DataArray)
				{
					if (const TSharedPtr<FJsonObject> MessageItObj = MessageValue->AsObject())
					{
						EDruidsSageChatRole Role = EDruidsSageChatRole::User;
						if (FString RoleString; MessageItObj->TryGetStringField(TEXT("role"), RoleString))
						{
							Role = UDruidsSageHelper::NameToRole(FName(*RoleString));
						}

						if (FString ContentString; MessageItObj->TryGetStringField(TEXT("content"), ContentString))
						{
							if (UIDruidsSageChatItem* ChatItem = CreateChatItem(Role, ContentString))
							{
								ChatItems.Add(ChatItem);
							}
						}
						else if (const TArray<TSharedPtr<FJsonValue>>* ContentArray; MessageItObj->TryGetArrayField(TEXT("content"), ContentArray))
						{
							for (const TSharedPtr ContentValue : *ContentArray)
							{
								if (const TSharedPtr<FJsonObject> ContentObj = ContentValue->AsObject())
								{
									if (FString ContentType;
										ContentObj->TryGetStringField(TEXT("type"), ContentType) &&
										ContentType == TEXT("text"))
									{
										if (FString TextContent;
											ContentObj->TryGetStringField(TEXT("text"), TextContent))
										{
											if (UIDruidsSageChatItem* ChatItem = CreateChatItem(Role, TextContent))
											{
												ChatItems.Add(ChatItem);
											}
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}

	// Add loaded items to the chat box
	if (ChatBox)
	{
		for (UIDruidsSageChatItem* Item : ChatItems)
		{
			if (Item)
			{
				ChatBox->AddChild(Item);
			}
		}
	}
}

void UDruidsSageChatView::SaveChatHistory() const
{
	const TSharedPtr<FJsonObject> JsonRequest = MakeShared<FJsonObject>();

	TArray<TSharedPtr<FJsonValue>> Data;
	for (const FDruidsSageChatMessage& Item : GetChatHistory())
	{
		Data.Add(Item.GetMessageJson());
	}

	if (!Data.IsEmpty())
	{
		JsonRequest->SetArrayField("Data", Data);
	}

	FString RequestContentString;
	const TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&RequestContentString);

	if (FJsonSerializer::Serialize(JsonRequest.ToSharedRef(), Writer))
	{
		// Ensure the directory exists
		const FString DirectoryPath = FPaths::GetPath(GetHistoryPath());
		if (!FPaths::DirectoryExists(DirectoryPath))
		{
			IFileManager::Get().MakeDirectory(*DirectoryPath, true);
		}

		FFileHelper::SaveStringToFile(RequestContentString, *GetHistoryPath());
	}
}


