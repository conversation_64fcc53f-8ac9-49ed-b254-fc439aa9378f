#include "ChatItems/DruidsSageSimpleChatItem.h"
#include <Dom/JsonObject.h>
#include <Serialization/JsonWriter.h>
#include <Serialization/JsonSerializer.h>

#include "Components/TextBlock.h"
#include "Components/ScrollBox.h"

#include "Framework/Application/SlateApplication.h"
#include "Windows/WindowsPlatformApplicationMisc.h"

#include "MarkdownRichTextBlock.h"

#include "DruidsSageMessagingHandler.h"

UDruidsSageSimpleChatItem::UDruidsSageSimpleChatItem(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
	, MessageRole(EDruidsSageChatRole::User)
	, ChatText(TEXT(""))
	, RoleWidget(nullptr)
	, MessageWidget(nullptr)
{
}

void UDruidsSageSimpleChatItem::NativePreConstruct()
{
	Super::NativePreConstruct();
	UpdateWidgetStyling();
}

void UDruidsSageSimpleChatItem::NativeConstruct()
{
	Super::NativeConstruct();
	UpdateWidgetStyling();
}

void UDruidsSageSimpleChatItem::SynchronizeProperties()
{
	Super::SynchronizeProperties();
	UpdateWidgetStyling();
}

FName UDruidsSageSimpleChatItem::GetTypeNameCpp() const
{
	return GetClassName();
}

void UDruidsSageSimpleChatItem::FillInDruidsMessage(FDruidsSageChatMessage& Message) const
{
	Message.SetRole(MessageRole);
	Message.SetChatContent(ChatText);
	Message.SetActiveExtensionDefinitions(ActiveExtensionDefinitions);
}

EDruidsSageChatRole UDruidsSageSimpleChatItem::GetMessageRole() const
{
	return MessageRole;
}

TWeakObjectPtr<UDruidsSageMessagingHandler> UDruidsSageSimpleChatItem::GetMessagingHandler() const
{
	return MessagingHandler;
}

void UDruidsSageSimpleChatItem::UpdateFromContentJson(const TSharedPtr<FJsonValue>& ContentJson)
{
	TSharedPtr<FJsonObject>* JsonObject = nullptr;
	if (!ContentJson.IsValid() || !ContentJson->TryGetObject(JsonObject) || !JsonObject)
	{
		return;
	}

	FString Type;
	(*JsonObject)->TryGetStringField(TEXT("type"), Type);
	if (Type == TEXT("text"))
	{
		FString Text;
		(*JsonObject)->TryGetStringField(TEXT("text"), Text);
		
		ChatText = Text;
		if (MessageWidget)
		{
			MessageWidget->SetNewText(FText::FromString(Text));
		}
	}
}

void UDruidsSageSimpleChatItem::InitializeSimpleChatItem(EDruidsSageChatRole InMessageRole, const FString& InChatText, 
	const TArray<TSharedPtr<FDruidsSageExtensionDefinition>>& InActiveExtensionDefinitions)
{
	MessageRole = InMessageRole;
	ChatText = InChatText;
	ActiveExtensionDefinitions = InActiveExtensionDefinitions;

	UpdateWidgetStyling();
}

void UDruidsSageSimpleChatItem::SetScrollBoxReference(UScrollBox* InScrollBox)
{
	SetupMessagingHandler(InScrollBox);
}

void UDruidsSageSimpleChatItem::SetupMessagingHandler(UScrollBox* ScrollBox)
{
	if (MessageRole == EDruidsSageChatRole::Assistant)
	{
		MessagingHandler = NewObject<UDruidsSageMessagingHandler>();
		MessagingHandler->SetFlags(RF_Standalone);
		MessagingHandler->ScrollBoxReference = ScrollBox;

		MessagingHandler->OnMessageContentUpdated.BindLambda([this](FString Content)
		{
			if (!MessageWidget)
			{
				return;
			}

			ChatText = Content;
			MessageWidget->SetNewText(FText::FromString(Content));
		});
	}
}

void UDruidsSageSimpleChatItem::UpdateWidgetStyling()
{
	if (!RoleWidget || !MessageWidget)
	{
		return;
	}

	FText RoleText = FText::FromString(TEXT("User:"));
	
	if (MessageRole == EDruidsSageChatRole::Assistant)
	{
		RoleText = FText::FromString(TEXT("Response:"));
	}
	else if (MessageRole == EDruidsSageChatRole::System)
	{
		RoleText = FText::FromString(TEXT("System:"));
	}

	RoleWidget->SetText(RoleText);
	MessageWidget->SetNewText(FText::FromString(ChatText));
}

void UDruidsSageSimpleChatItem::CopyMarkdownToClipboard()
{
	if (MessageWidget)
	{
		FString MarkdownText = MessageWidget->GetOriginalMarkdownText();
		if (!MarkdownText.IsEmpty())
		{
			FPlatformApplicationMisc::ClipboardCopy(*MarkdownText);
		}
	}
}

void UDruidsSageSimpleChatItem::CopyPlainTextToClipboard()
{
	if (MessageWidget)
	{
		FString PlainText = MessageWidget->GetPlainText();
		if (!PlainText.IsEmpty())
		{
			FPlatformApplicationMisc::ClipboardCopy(*PlainText);
		}
	}
}
