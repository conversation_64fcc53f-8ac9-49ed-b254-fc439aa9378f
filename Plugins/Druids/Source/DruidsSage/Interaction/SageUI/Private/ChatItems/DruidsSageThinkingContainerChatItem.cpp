#include "ChatItems/DruidsSageThinkingContainerChatItem.h"
#include <Dom/JsonObject.h>
#include <Serialization/JsonWriter.h>
#include <Serialization/JsonSerializer.h>

#include "LogDruids.h"

#include "Components/ScrollBox.h"
#include "Components/VerticalBox.h"
#include "Components/VerticalBoxSlot.h"
#include "Components/TextBlock.h"

#include "SageUIModule.h"
#include "ChatWidgetOverrides.h"
#include "DruidsSageMessagingHandler.h"
#include "SimpleJSON.h"
#include "SimpleJSON.h"
#include "ChatItems/DruidsSageThinkingChatItem.h"

UDruidsSageThinkingContainerChatItem::UDruidsSageThinkingContainerChatItem(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
	, ContainerTitle(TEXT("Thinking"))
	, TitleWidget(nullptr)
	, ThinkingContainer(nullptr)
{
}

void UDruidsSageThinkingContainerChatItem::NativePreConstruct()
{
	Super::NativePreConstruct();
	UpdateWidgetStyling();
}

void UDruidsSageThinkingContainerChatItem::NativeConstruct()
{
	Super::NativeConstruct();
	UpdateWidgetStyling();
}

void UDruidsSageThinkingContainerChatItem::SynchronizeProperties()
{
	Super::SynchronizeProperties();
	UpdateWidgetStyling();
}

FName UDruidsSageThinkingContainerChatItem::GetTypeName() const
{
	return GetClassName();
}

void UDruidsSageThinkingContainerChatItem::FillInDruidsMessage(FDruidsSageChatMessage& Message) const
{
	Message.SetRole(EDruidsSageChatRole::Assistant);
	Message.SetChatContent(ContainerTitle);
}

EDruidsSageChatRole UDruidsSageThinkingContainerChatItem::GetMessageRole() const
{
	return EDruidsSageChatRole::Assistant;
}

TWeakObjectPtr<UDruidsSageMessagingHandler> UDruidsSageThinkingContainerChatItem::GetMessagingHandler() const
{
	return MessagingHandler;
}

void UDruidsSageThinkingContainerChatItem::UpdateFromContentJson(const TSharedPtr<FJsonValue>& ContentJson)
{
	if (!ContentJson.IsValid())
	{
		return;
	}

	SimpleJSON ContentSimpleJSON(ContentJson);

	if (ContentSimpleJSON["type"].AsString() == TEXT("thinking_container"))
	{
		if (ContentSimpleJSON["title"].IsString())
		{
			ContainerTitle = ContentSimpleJSON["title"].AsString();
			UpdateWidgetStyling();
		}

		// Process thinking items using incremental update pattern
		if (ContentSimpleJSON["thinking_items"].IsArray())
		{
			TArray<SimpleJSON> ThinkingItemJsons = ContentSimpleJSON["thinking_items"].AsArray();
			for (int32 ThinkingItemIndex = 0; ThinkingItemIndex < ThinkingItemJsons.Num(); ThinkingItemIndex++)
			{
				SimpleJSON ThinkingItem = ThinkingItemJsons[ThinkingItemIndex];
				if (ThinkingItem["content"].IsArray())
				{
					if (ThinkingItemIndex < ThinkingItems.Num())
					{
						UDruidsSageThinkingChatItem* ExistingItem = ThinkingItems[ThinkingItemIndex];
						if (ExistingItem)
						{
							ExistingItem->UpdateFromContentJson(ThinkingItem.GetJsonValue());
						}
					}
					else
					{
						// Add new thinking item
						if (UDruidsSageThinkingChatItem* NewThinkingItem = CreateThinkingItem(ThinkingItem.GetJsonValue()))
						{
							AddThinkingItem(NewThinkingItem);
						}
					}
				}
			}
		}
	}
}

void UDruidsSageThinkingContainerChatItem::InitializeThinkingContainerChatItem(const FString& InContainerTitle)
{
	ContainerTitle = InContainerTitle;
	UpdateWidgetStyling();
}

void UDruidsSageThinkingContainerChatItem::SetScrollBoxReference(class UScrollBox* InScrollBox)
{
	SetupMessagingHandler(InScrollBox);
}

void UDruidsSageThinkingContainerChatItem::AddThinkingItem(class UDruidsSageThinkingChatItem* ThinkingItem)
{
	if (ThinkingItem && ThinkingContainer)
	{
		ThinkingItems.Add(ThinkingItem);
		ThinkingContainer->AddChild(ThinkingItem);
	}
}

void UDruidsSageThinkingContainerChatItem::ClearThinkingItems()
{
	if (ThinkingContainer)
	{
		ThinkingContainer->ClearChildren();
	}
	ThinkingItems.Empty();
}

void UDruidsSageThinkingContainerChatItem::SetupMessagingHandler(class UScrollBox* ScrollBox)
{
	if (ScrollBox)
	{
		MessagingHandler = NewObject<UDruidsSageMessagingHandler>(this);
		if (MessagingHandler.IsValid())
		{
			MessagingHandler->ScrollBoxReference = ScrollBox;
		}
	}
}

void UDruidsSageThinkingContainerChatItem::UpdateWidgetStyling()
{
	if (TitleWidget)
	{
		TitleWidget->SetText(FText::FromString(ContainerTitle));
	}
}

UDruidsSageThinkingChatItem* UDruidsSageThinkingContainerChatItem::CreateThinkingItem(const TSharedPtr<FJsonValue>& ContentJson)
{
	//NOTE: Hold this variable out here so it stays alive long enough to create the widget
	UChatWidgetOverrides* Overrides;
	
	// Get the widget class from overrides
	TSubclassOf<UDruidsSageThinkingChatItem> WidgetClass = UDruidsSageThinkingChatItem::StaticClass();
	if (FSageUIModule* SageUIModule = FModuleManager::GetModulePtr<FSageUIModule>("SageUI"))
	{
		Overrides = SageUIModule->GetChatWidgetOverrides();
		if (Overrides)
		{
			if (TSubclassOf<UDruidsSageThinkingChatItem> OverrideClass = Overrides->GetThinkingChatItemWidgetClass())
			{
				WidgetClass = OverrideClass;
			}
		}
	}

	UDruidsSageThinkingChatItem* ThinkingItem = CreateWidget<UDruidsSageThinkingChatItem>(this, WidgetClass);
	if (ThinkingItem)
	{
		ThinkingItem->UpdateFromContentJson(ContentJson);
	}
	return ThinkingItem;
}
