#include "ChatItems/DruidsSageThinkingChatItem.h"
#include <Dom/JsonObject.h>
#include <Serialization/JsonWriter.h>
#include <Serialization/JsonSerializer.h>

#include "Components/ScrollBox.h"

#include "SageUIModule.h"
#include "ChatWidgetOverrides.h"
#include "DruidsSageMessagingHandler.h"
#include "MarkdownRichTextBlock.h"
#include "SimpleJSON.h"
#include "ChatItems/DruidsSageThinkingDetailChatItem.h"
#include "Components/VerticalBox.h"

UDruidsSageThinkingChatItem::UDruidsSageThinkingChatItem(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
	, SummaryWidget(nullptr)
	, DetailContainer(nullptr)
{
}

void UDruidsSageThinkingChatItem::NativePreConstruct()
{
	Super::NativePreConstruct();
	UpdateWidgetStyling();
}

void UDruidsSageThinkingChatItem::NativeConstruct()
{
	Super::NativeConstruct();
	UpdateWidgetStyling();
}

void UDruidsSageThinkingChatItem::SynchronizeProperties()
{
	Super::SynchronizeProperties();
	UpdateWidgetStyling();
}

FName UDruidsSageThinkingChatItem::GetTypeName() const
{
	return GetClassName();
}

void UDruidsSageThinkingChatItem::FillInDruidsMessage(FDruidsSageChatMessage& Message) const
{
	Message.SetRole(EDruidsSageChatRole::Assistant);
	Message.SetChatContent(SummaryText);
}

EDruidsSageChatRole UDruidsSageThinkingChatItem::GetMessageRole() const
{
	return EDruidsSageChatRole::Assistant;
}

TWeakObjectPtr<UDruidsSageMessagingHandler> UDruidsSageThinkingChatItem::GetMessagingHandler() const
{
	return MessagingHandler;
}

void UDruidsSageThinkingChatItem::UpdateFromContentJson(const TSharedPtr<FJsonValue>& ContentJson)
{
	if (!ContentJson.IsValid())
	{
		return;
	}

	SimpleJSON ThinkingItemSimpleJSON(ContentJson);
	if (ThinkingItemSimpleJSON["content"].IsArray())
	{
		int32 NumberOfTextItemsPresent = 0;
		TArray<SimpleJSON> ContentSimpleJSONs = ThinkingItemSimpleJSON["content"].AsArray();
		for (int32 ContentItemIndex = 0; ContentItemIndex < ContentSimpleJSONs.Num(); ContentItemIndex++)
		{
			SimpleJSON ContentSimpleJSON = ContentSimpleJSONs[ContentItemIndex];
			if (FString ContentType = ContentSimpleJSON["type"].AsString(); ContentType == TEXT("summary"))
			{
				SummaryText = ContentSimpleJSON["text"].AsString();
				UpdateWidgetStyling();
			}
			else if (ContentType == TEXT("text"))
			{
				if (!DetailContainer)
				{
					continue;
				}

				if (NumberOfTextItemsPresent < DetailItems.Num())
				{
					// Update existing detail item
					if (UDruidsSageThinkingDetailChatItem* ExistingDetail = DetailItems[NumberOfTextItemsPresent])
					{
						ExistingDetail->UpdateFromContentJson(ContentSimpleJSON.GetJsonValue());
					}
				}
				else
				{
					// Add new detail item
					if (UDruidsSageThinkingDetailChatItem* NewDetailItem = CreateDetailItem(ContentSimpleJSON.GetJsonValue()))
					{
						AddThinkingDetailItem(NewDetailItem);
					}

				}
			}
		}
	}
	
	const TSharedPtr<FJsonObject> ContentObject = ContentJson->AsObject();
	if (!ContentObject.IsValid())
	{
		return;
	}

	FString ContentType;
	if (ContentObject->TryGetStringField(TEXT("type"), ContentType))
	{
		if (ContentType == TEXT("thinking"))
		{
			// Update summary text
			FString SummaryContent;
			if (ContentObject->TryGetStringField(TEXT("summary"), SummaryContent))
			{
				SummaryText = SummaryContent;
				UpdateWidgetStyling();
			}

			// Process detail items using incremental update pattern
			const TArray<TSharedPtr<FJsonValue>>* DetailArray;
			if (ContentObject->TryGetArrayField(TEXT("details"), DetailArray))
			{
				if (!DetailContainer)
				{
					return;
				}

				int32 NumExistingDetails = DetailItems.Num();

				for (int32 DetailIndex = 0; DetailIndex < DetailArray->Num(); DetailIndex++)
				{
					const TSharedPtr<FJsonValue>& DetailValue = (*DetailArray)[DetailIndex];

					if (DetailIndex < NumExistingDetails)
					{
						// Update existing detail item
						UDruidsSageThinkingDetailChatItem* ExistingDetail = DetailItems[DetailIndex];
						if (ExistingDetail)
						{
							ExistingDetail->UpdateFromContentJson(DetailValue);
						}
					}
					else
					{
						// Add new detail item
						if (UDruidsSageThinkingDetailChatItem* NewDetailItem = CreateDetailItem(DetailValue))
						{
							AddThinkingDetailItem(NewDetailItem);
						}
					}
				}
			}
		}
	}
}

void UDruidsSageThinkingChatItem::InitializeThinkingChatItem(const FString& InSummaryText)
{
	SummaryText = InSummaryText;
	UpdateWidgetStyling();
}

void UDruidsSageThinkingChatItem::SetScrollBoxReference(class UScrollBox* InScrollBox)
{
	SetupMessagingHandler(InScrollBox);
}

void UDruidsSageThinkingChatItem::AddThinkingDetailItem(class UDruidsSageThinkingDetailChatItem* DetailItem)
{
	if (DetailItem && DetailContainer)
	{
		DetailItems.Add(DetailItem);
		DetailContainer->AddChild(DetailItem);
	}
}

void UDruidsSageThinkingChatItem::ClearDetailItems()
{
	if (DetailContainer)
	{
		DetailContainer->ClearChildren();
	}
	DetailItems.Empty();
}

void UDruidsSageThinkingChatItem::SetupMessagingHandler(class UScrollBox* ScrollBox)
{
	if (ScrollBox)
	{
		MessagingHandler = NewObject<UDruidsSageMessagingHandler>(this);
		if (MessagingHandler.IsValid())
		{
			MessagingHandler->ScrollBoxReference = ScrollBox;
		}
	}
}

void UDruidsSageThinkingChatItem::UpdateWidgetStyling()
{
	if (SummaryWidget)
	{
		SummaryWidget->SetNewText(FText::FromString(SummaryText));
	}
}

UDruidsSageThinkingDetailChatItem* UDruidsSageThinkingChatItem::CreateDetailItem(const TSharedPtr<FJsonValue>& ContentJson)
{
	//NOTE: Hold this variable out here so it stays alive long enough to create the widget
	UChatWidgetOverrides* Overrides;
	
	// Get the widget class from overrides
	TSubclassOf<UDruidsSageThinkingDetailChatItem> WidgetClass = UDruidsSageThinkingDetailChatItem::StaticClass();
	if (FSageUIModule* SageUIModule = FModuleManager::GetModulePtr<FSageUIModule>("SageUI"))
	{
		Overrides = SageUIModule->GetChatWidgetOverrides();
		if (Overrides)
		{
			if (TSubclassOf<UDruidsSageThinkingDetailChatItem> OverrideClass = Overrides->GetThinkingDetailChatItemWidgetClass())
			{
				WidgetClass = OverrideClass;
			}
		}
	}

	UDruidsSageThinkingDetailChatItem* DetailItem = CreateWidget<UDruidsSageThinkingDetailChatItem>(this, WidgetClass);
	if (DetailItem)
	{
		DetailItem->UpdateFromContentJson(ContentJson);
	}
	return DetailItem;
}
