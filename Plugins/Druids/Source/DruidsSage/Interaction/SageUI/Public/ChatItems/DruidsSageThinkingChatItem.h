#pragma once

#include <CoreMinimal.h>

#include "DruidsSageChatTypes.h"
#include "IDruidsSageChatItem.h"

#include "DruidsSageThinkingChatItem.generated.h"

class UMarkdownRichTextBlock;
class UDruidsSageMessagingHandler;
class UTextBlock;
class UVerticalBox;
class UScrollBox;
struct FDruidsSageExtensionDefinition;

UCLASS(BlueprintType, Blueprintable)
class SAGEUI_API UDruidsSageThinkingChatItem : public UIDruidsSageChatItem
{
	GENERATED_BODY()

public:
	UDruidsSageThinkingChatItem(const FObjectInitializer& ObjectInitializer);

	// UUserWidget interface
	virtual void NativePreConstruct() override;
	virtual void NativeConstruct() override;
	virtual void SynchronizeProperties() override;
	// End of UUserWidget interface

	// UIDruidsSageChatItem interface implementation
	virtual FName GetTypeName() const override;
	virtual void FillInDruidsMessage(FDruidsSageChatMessage& Message) const override;
	virtual EDruidsSageChatRole GetMessageRole() const override;
	virtual TWeakObjectPtr<UDruidsSageMessagingHandler> GetMessagingHandler() const override;
	virtual void UpdateFromContentJson(const TSharedPtr<FJsonValue>& ContentJson) override;

	// Configuration properties
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chat Item")
	FString SummaryText;

	// Initialization methods
	void InitializeThinkingChatItem(const FString& InSummaryText);

	UFUNCTION(BlueprintCallable, Category = "Chat Item")
	void SetScrollBoxReference(class UScrollBox* InScrollBox);

	// Container management
	UFUNCTION(BlueprintCallable, Category = "Chat Item")
	void AddThinkingDetailItem(class UDruidsSageThinkingDetailChatItem* DetailItem);

	UFUNCTION(BlueprintCallable, Category = "Chat Item")
	void ClearDetailItems();

	static FName GetClassName() { return "UDruidsSageThinkingChatItem"; }

protected:
	UPROPERTY(meta = (BindWidget), BlueprintReadOnly, Category = "Chat Item")
	UMarkdownRichTextBlock* SummaryWidget;

	UPROPERTY(meta = (BindWidget), BlueprintReadOnly, Category = "Chat Item")
	UVerticalBox* DetailContainer;

private:
	TWeakObjectPtr<UDruidsSageMessagingHandler> MessagingHandler;
	TArray<class UDruidsSageThinkingDetailChatItem*> DetailItems;

	void SetupMessagingHandler(class UScrollBox* ScrollBox);
	void UpdateWidgetStyling();
	class UDruidsSageThinkingDetailChatItem* CreateDetailItem(const TSharedPtr<FJsonValue>& ContentJson);
};
