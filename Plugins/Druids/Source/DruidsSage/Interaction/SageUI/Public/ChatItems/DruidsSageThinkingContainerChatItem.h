#pragma once

#include <CoreMinimal.h>

#include "DruidsSageChatTypes.h"
#include "IDruidsSageChatItem.h"

#include "DruidsSageThinkingContainerChatItem.generated.h"

class UDruidsSageMessagingHandler;
class UTextBlock;
class UVerticalBox;
class UScrollBox;
struct FDruidsSageExtensionDefinition;

UCLASS(BlueprintType, Blueprintable)
class SAGEUI_API UDruidsSageThinkingContainerChatItem : public UIDruidsSageChatItem
{
	GENERATED_BODY()

public:
	UDruidsSageThinkingContainerChatItem(const FObjectInitializer& ObjectInitializer);

	// UUserWidget interface
	virtual void NativePreConstruct() override;
	virtual void NativeConstruct() override;
	virtual void SynchronizeProperties() override;
	// End of UUserWidget interface

	// UIDruidsSageChatItem interface implementation
	virtual FName GetTypeName() const override;
	virtual void FillInDruidsMessage(FDruidsSageChatMessage& Message) const override;
	virtual EDruidsSageChatRole GetMessageRole() const override;
	virtual TWeakObjectPtr<UDruidsSageMessagingHandler> GetMessagingHandler() const override;
	virtual void UpdateFromContentJson(const TSharedPtr<FJsonValue>& ContentJson) override;

	// Configuration properties
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chat Item")
	FString ContainerTitle;

	// Initialization methods
	void InitializeThinkingContainerChatItem(const FString& InContainerTitle = TEXT("Thinking"));

	UFUNCTION(BlueprintCallable, Category = "Chat Item")
	void SetScrollBoxReference(class UScrollBox* InScrollBox);

	// Container management
	UFUNCTION(BlueprintCallable, Category = "Chat Item")
	void AddThinkingItem(class UDruidsSageThinkingChatItem* ThinkingItem);

	UFUNCTION(BlueprintCallable, Category = "Chat Item")
	void ClearThinkingItems();

	static FName GetClassName() { return "UDruidsSageThinkingContainerChatItem"; }

protected:
	UPROPERTY(meta = (BindWidget), BlueprintReadOnly, Category = "Chat Item")
	UTextBlock* TitleWidget;

	UPROPERTY(meta = (BindWidget), BlueprintReadOnly, Category = "Chat Item")
	UVerticalBox* ThinkingContainer;

private:
	TWeakObjectPtr<UDruidsSageMessagingHandler> MessagingHandler;
	TArray<class UDruidsSageThinkingChatItem*> ThinkingItems;

	void SetupMessagingHandler(class UScrollBox* ScrollBox);
	void UpdateWidgetStyling();
	class UDruidsSageThinkingChatItem* CreateThinkingItem(const TSharedPtr<FJsonValue>& ContentJson);
};
