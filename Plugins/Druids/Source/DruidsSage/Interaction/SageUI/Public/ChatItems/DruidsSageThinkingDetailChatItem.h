#pragma once

#include <CoreMinimal.h>

#include "DruidsSageChatTypes.h"
#include "IDruidsSageChatItem.h"

#include "DruidsSageThinkingDetailChatItem.generated.h"

class UMarkdownRichTextBlock;
class UDruidsSageMessagingHandler;
class UTextBlock;
struct FDruidsSageExtensionDefinition;

UCLASS(BlueprintType, Blueprintable)
class SAGEUI_API UDruidsSageThinkingDetailChatItem : public UIDruidsSageChatItem
{
	GENERATED_BODY()

public:
	UDruidsSageThinkingDetailChatItem(const FObjectInitializer& ObjectInitializer);

	// UUserWidget interface
	virtual void NativePreConstruct() override;
	virtual void NativeConstruct() override;
	virtual void SynchronizeProperties() override;
	// End of UUserWidget interface

	// UIDruidsSageChatItem interface implementation
	virtual FName GetTypeName() const override;
	virtual void FillInDruidsMessage(FDruidsSageChatMessage& Message) const override;
	virtual EDruidsSageChatRole GetMessageRole() const override;
	virtual TWeakObjectPtr<UDruidsSageMessagingHandler> GetMessagingHandler() const override;
	virtual void UpdateFromContentJson(const TSharedPtr<FJsonValue>& ContentJson) override;
	virtual FString GetPlainText() const override;

	// Configuration properties
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chat Item")
	FString DetailText;

	// Initialization methods
	void InitializeThinkingDetailChatItem(const FString& InDetailText);

	UFUNCTION(BlueprintCallable, Category = "Chat Item")
	void SetScrollBoxReference(class UScrollBox* InScrollBox);

	static FName GetClassName() { return "UDruidsSageThinkingDetailChatItem"; }

protected:
	UPROPERTY(meta = (BindWidget), BlueprintReadOnly, Category = "Chat Item")
	UMarkdownRichTextBlock* DetailWidget;

private:
	TWeakObjectPtr<UDruidsSageMessagingHandler> MessagingHandler;

	void SetupMessagingHandler(class UScrollBox* ScrollBox);
	void UpdateWidgetStyling();
};
