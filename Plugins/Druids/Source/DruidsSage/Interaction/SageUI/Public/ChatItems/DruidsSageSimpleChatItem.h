#pragma once

#include <CoreMinimal.h>

#include "DruidsSageChatTypes.h"
#include "IDruidsSageChatItem.h"

#include "DruidsSageSimpleChatItem.generated.h"

class UMarkdownRichTextBlock;
class UDruidsSageMessagingHandler;
class UTextBlock;
struct FDruidsSageExtensionDefinition;

UCLASS(BlueprintType, Blueprintable)
class SAGEUI_API UDruidsSageSimpleChatItem : public UIDruidsSageChatItem
{
	GENERATED_BODY()

public:
	UDruidsSageSimpleChatItem(const FObjectInitializer& ObjectInitializer);

	// UUserWidget interface
	virtual void NativePreConstruct() override;
	virtual void NativeConstruct() override;
	virtual void SynchronizeProperties() override;
	// End of UUserWidget interface

	// UIDruidsSageChatItem interface implementation
	virtual FName GetTypeNameCpp() const override;
	virtual void FillInDruidsMessage(FDruidsSageChatMessage& Message) const override;
	virtual EDruidsSageChatRole GetMessageRole() const override;
	virtual TWeakObjectPtr<UDruidsSageMessagingHandler> GetMessagingHandler() const override;
	virtual void UpdateFromContentJson(const TSharedPtr<FJsonValue>& ContentJson) override;

	// Configuration properties
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chat Item")
	EDruidsSageChatRole MessageRole = EDruidsSageChatRole::User;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chat Item")
	FString ChatText;

	// Initialization methods
	void InitializeSimpleChatItem(EDruidsSageChatRole InMessageRole, const FString& InChatText, 
		const TArray<TSharedPtr<FDruidsSageExtensionDefinition>>& InActiveExtensionDefinitions);

	UFUNCTION(BlueprintCallable, Category = "Chat Item")
	void SetScrollBoxReference(class UScrollBox* InScrollBox);

	// Copy methods that can be called from Blueprint or C++
	UFUNCTION(BlueprintCallable, Category = "Chat Item")
	void CopyMarkdownToClipboard();

	UFUNCTION(BlueprintCallable, Category = "Chat Item")
	void CopyPlainTextToClipboard();

	static FName GetClassName() { return "UDruidsSageSimpleChatItem"; }

protected:
	// BindWidget properties for Blueprint binding
	UPROPERTY(meta = (BindWidget), BlueprintReadOnly, Category = "Chat Item")
	UTextBlock* RoleWidget;

	UPROPERTY(meta = (BindWidget), BlueprintReadOnly, Category = "Chat Item")
	UMarkdownRichTextBlock* MessageWidget;

private:
	TWeakObjectPtr<UDruidsSageMessagingHandler> MessagingHandler;
	TArray<TSharedPtr<FDruidsSageExtensionDefinition>> ActiveExtensionDefinitions;

	void SetupMessagingHandler(class UScrollBox* ScrollBox);
	void UpdateWidgetStyling();
};
