#include "ChatRequestHandler_V2.h"
#include "DruidsSageMessagingHandler.h"
#include "DruidsSageHelper.h"
#include "IDruidsSageChatItem.h"
#include "SageExtensionDelegator.h"

bool ChatRequestHandler_V2::IsNoActiveRequest() const
{
    return !RequestReference.IsValid() || !UDruidsSageTaskStatus::IsTaskActive(RequestReference.Get());
}

void ChatRequestHandler_V2::StopAndCleanupRequest(TArray<UIDruidsSageChatItem*> ChatItems)
{
    if (RequestReference.IsValid())
    {
        // Find the last assistant message to get its messaging handler
        for (UIDruidsSageChatItem* ChatItem : ChatItems)
        {
            if (ChatItem)
            {
                if (ChatItem->GetMessageRole() == EDruidsSageChatRole::Assistant && ChatItem->GetMessagingHandler().IsValid())
                {
                    // Unbind all delegates
                    ChatItem->GetMessagingHandler()->OnMessageRequestSent.Unbind();
                    ChatItem->GetMessagingHandler()->OnMessageRequestFailed.Unbind();
                    ChatItem->GetMessagingHandler()->OnMessageContentUpdated.Unbind();
                    ChatItem->GetMessagingHandler()->OnMessageResponseUpdated.Unbind();

                    RequestReference->ProgressStarted.RemoveAll(ChatItem->GetMessagingHandler().Get());
                    RequestReference->ProgressUpdated.RemoveAll(ChatItem->GetMessagingHandler().Get());
                    RequestReference->ProcessCompleted.RemoveAll(ChatItem->GetMessagingHandler().Get());
                    RequestReference->ErrorReceived.RemoveAll(ChatItem->GetMessagingHandler().Get());
                    RequestReference->RequestFailed.RemoveAll(ChatItem->GetMessagingHandler().Get());
                    RequestReference->RequestSent.RemoveAll(ChatItem->GetMessagingHandler().Get());
                }
            }
        }
        
        RequestReference->StopDruidsSageTask();
        RequestReference.Reset();
    }
}

void ChatRequestHandler_V2::SetupAndSendRequest(TArray<UIDruidsSageChatItem*> ChatItems,
                                                 UIDruidsSageChatItem* AssistantMessage, const FString& Context)
{
    if (!AssistantMessage || !AssistantMessage->GetMessagingHandler().IsValid())
    {
        return;
    }

    TArray<FDruidsSageChatMessage> ChatHistory;
	for (UIDruidsSageChatItem* Item : ChatItems)
    {
        if (Item)
        {
            FString MessageRoleText = UDruidsSageHelper::RoleToName(Item->GetMessageRole()).ToString();

	        FDruidsSageChatMessage ChatMessage;
	        Item->FillInDruidsMessage(ChatMessage);

            // Add UserFocusContext to user messages
            if (Item->GetMessageRole() == EDruidsSageChatRole::User && !Context.IsEmpty())
            {
                ChatMessage.SetUserFocusContext(Context);
            }

            ChatHistory.Add(ChatMessage);
        }
    }

    ExtensionDelegator = MakeShared<FSageExtensionDelegator>();
    RequestReference = UDruidsSageChatRequest_v2::EditorTask(ChatHistory, ExtensionDelegator, Context);

    RequestReference->ProgressStarted.AddDynamic(
        AssistantMessage->GetMessagingHandler().Get(),
        &UDruidsSageMessagingHandler::ProcessUpdated);

    RequestReference->ProgressUpdated.AddDynamic(
        AssistantMessage->GetMessagingHandler().Get(),
        &UDruidsSageMessagingHandler::ProcessUpdated);

    RequestReference->ProcessCompleted.AddDynamic(
        AssistantMessage->GetMessagingHandler().Get(),
        &UDruidsSageMessagingHandler::ProcessCompleted);

    RequestReference->ErrorReceived.AddDynamic(
        AssistantMessage->GetMessagingHandler().Get(),
        &UDruidsSageMessagingHandler::ProcessCompleted);

    RequestReference->RequestFailed.AddDynamic(
        AssistantMessage->GetMessagingHandler().Get(),
        &UDruidsSageMessagingHandler::RequestFailed);

    RequestReference->RequestSent.AddDynamic(
        AssistantMessage->GetMessagingHandler().Get(),
        &UDruidsSageMessagingHandler::RequestSent);

    RequestReference->Activate();
}