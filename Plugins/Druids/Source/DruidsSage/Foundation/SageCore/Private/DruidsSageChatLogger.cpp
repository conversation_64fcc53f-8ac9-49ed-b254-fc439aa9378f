#include "DruidsSageChatLogger.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/FileHelper.h"
#include "Misc/Paths.h"
#include "Misc/DateTime.h"

UDruidsSageChatLogger* UDruidsSageChatLogger::Instance = nullptr;

UDruidsSageChatLogger::UDruidsSageChatLogger()
{
	// Set flags to prevent garbage collection
	SetFlags(RF_Standalone);
	
	// Load existing chat log
	LoadChatLog();
}

void UDruidsSageChatLogger::LogChatExchange(const FString& UserRequest, const FString& AssistantResponse)
{
	// Skip empty exchanges
	if (UserRequest.IsEmpty() && AssistantResponse.IsEmpty())
	{
		return;
	}

	// Add new entry
	FChatLogEntry NewEntry(UserRequest, AssistantResponse);
	ChatLogEntries.Add(NewEntry);

	// Trim to max entries
	TrimLogToMaxEntries();

	// Save to file
	SaveChatLog();
}

FString UDruidsSageChatLogger::GetChatLogPath()
{
	return FPaths::Combine(FPaths::ProjectSavedDir(), TEXT("DruidsSage"), TEXT("ChatLog.md"));
}

UDruidsSageChatLogger* UDruidsSageChatLogger::GetInstance()
{
	if (!Instance)
	{
		Instance = NewObject<UDruidsSageChatLogger>();
	}
	return Instance;
}

void UDruidsSageChatLogger::LoadChatLog()
{
	FString ChatLogPath = GetChatLogPath();
	FString LogContent;
	
	if (FFileHelper::LoadFileToString(LogContent, *ChatLogPath))
	{
		// Parse the markdown content to extract entries
		TArray<FString> Lines;
		LogContent.ParseIntoArrayLines(Lines);
		
		FString CurrentUserRequest;
		FString CurrentAssistantResponse;
		bool bParsingUser = false;
		bool bParsingAssistant = false;
		
		for (const FString& Line : Lines)
		{
			if (Line.StartsWith(TEXT("### Request")))
			{
				// Save previous entry if we have one
				if (!CurrentUserRequest.IsEmpty() || !CurrentAssistantResponse.IsEmpty())
				{
					ChatLogEntries.Add(FChatLogEntry(CurrentUserRequest, CurrentAssistantResponse));
				}
				
				// Reset for new entry
				CurrentUserRequest.Empty();
				CurrentAssistantResponse.Empty();
				bParsingUser = false;
				bParsingAssistant = false;
			}
			else if (Line.StartsWith(TEXT("**User**:")))
			{
				bParsingUser = true;
				bParsingAssistant = false;
				// Extract user request (remove "**User**: " prefix)
				CurrentUserRequest = Line.Mid(9); // "**User**: " is 9 characters
			}
			else if (Line.StartsWith(TEXT("**Assistant**:")))
			{
				bParsingUser = false;
				bParsingAssistant = true;
				// Extract assistant response (remove "**Assistant**: " prefix)
				CurrentAssistantResponse = Line.Mid(14); // "**Assistant**: " is 14 characters
			}
			else if (bParsingUser && !Line.IsEmpty())
			{
				// Continue user request on new line
				if (!CurrentUserRequest.IsEmpty())
				{
					CurrentUserRequest += TEXT("\n");
				}
				CurrentUserRequest += Line;
			}
			else if (bParsingAssistant && !Line.IsEmpty())
			{
				// Continue assistant response on new line
				if (!CurrentAssistantResponse.IsEmpty())
				{
					CurrentAssistantResponse += TEXT("\n");
				}
				CurrentAssistantResponse += Line;
			}
		}
		
		// Save the last entry
		if (!CurrentUserRequest.IsEmpty() || !CurrentAssistantResponse.IsEmpty())
		{
			ChatLogEntries.Add(FChatLogEntry(CurrentUserRequest, CurrentAssistantResponse));
		}
	}
	
	// Trim to max entries in case the file had more
	TrimLogToMaxEntries();
}

void UDruidsSageChatLogger::SaveChatLog()
{
	EnsureLogDirectoryExists();
	
	FString ChatLogPath = GetChatLogPath();
	FString LogContent;
	
	// Build markdown content
	for (const FChatLogEntry& Entry : ChatLogEntries)
	{
		LogContent += TEXT("### Request\n");
		LogContent += FString::Printf(TEXT("**User**: %s\n"), *Entry.UserRequest);
		LogContent += FString::Printf(TEXT("**Assistant**: %s\n"), *Entry.AssistantResponse);
		LogContent += TEXT("\n");
	}
	
	// Save to file
	FFileHelper::SaveStringToFile(LogContent, *ChatLogPath);
}

void UDruidsSageChatLogger::EnsureLogDirectoryExists()
{
	FString LogDir = FPaths::GetPath(GetChatLogPath());
	IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
	
	if (!PlatformFile.DirectoryExists(*LogDir))
	{
		PlatformFile.CreateDirectoryTree(*LogDir);
	}
}

void UDruidsSageChatLogger::TrimLogToMaxEntries()
{
	while (ChatLogEntries.Num() > MaxLogEntries)
	{
		ChatLogEntries.RemoveAt(0);
	}
}
