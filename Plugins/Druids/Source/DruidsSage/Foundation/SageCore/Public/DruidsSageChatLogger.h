#pragma once

#include <CoreMinimal.h>
#include <UObject/NoExportTypes.h>

#include "DruidsSageChatLogger.generated.h"

/**
 * <PERSON>les logging of chat conversations to a markdown file
 * Maintains the last 100 chat exchanges in /Saved/DruidsSage/ChatLog.md
 */
UCLASS()
class SAGECORE_API UDruidsSageChatLogger : public UObject
{
	GENERATED_BODY()

public:
	UDruidsSageChatLogger();

	/**
	 * Log a completed chat exchange (user request + assistant response)
	 * @param UserRequest The plain text of the user's request
	 * @param AssistantResponse The plain text of the assistant's response
	 */
	UFUNCTION(BlueprintCallable, Category = "Chat Logger")
	void LogChatExchange(const FString& UserRequest, const FString& AssistantResponse);

	/**
	 * Get the path to the chat log file
	 */
	UFUNCTION(BlueprintCallable, Category = "Chat Logger")
	static FString GetChatLogPath();

	/**
	 * Get the singleton instance of the chat logger
	 */
	static UDruidsSageChatLogger* GetInstance();

private:
	/**
	 * Load existing chat log entries from file
	 */
	void LoadChatLog();

	/**
	 * Save chat log entries to file
	 */
	void SaveChatLog();

	/**
	 * Ensure the chat log directory exists
	 */
	void EnsureLogDirectoryExists();

	/**
	 * Trim the log to maintain only the last 100 entries
	 */
	void TrimLogToMaxEntries();

	/**
	 * Structure to hold a single chat exchange
	 */
	USTRUCT()
	struct FChatLogEntry
	{
		GENERATED_BODY()

		UPROPERTY()
		FString UserRequest;

		UPROPERTY()
		FString AssistantResponse;

		UPROPERTY()
		FDateTime Timestamp;

		FChatLogEntry()
			: Timestamp(FDateTime::Now())
		{
		}

		FChatLogEntry(const FString& InUserRequest, const FString& InAssistantResponse)
			: UserRequest(InUserRequest)
			, AssistantResponse(InAssistantResponse)
			, Timestamp(FDateTime::Now())
		{
		}
	};

	/**
	 * Array of chat log entries
	 */
	UPROPERTY()
	TArray<FChatLogEntry> ChatLogEntries;

	/**
	 * Maximum number of entries to keep in the log
	 */
	static constexpr int32 MaxLogEntries = 100;

	/**
	 * Singleton instance
	 */
	static UDruidsSageChatLogger* Instance;
};
