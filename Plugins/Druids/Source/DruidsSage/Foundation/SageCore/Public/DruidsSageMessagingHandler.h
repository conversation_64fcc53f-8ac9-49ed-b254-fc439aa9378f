#pragma once

#include <CoreMinimal.h>
#include <UObject/Object.h>
#include <Components/ScrollBox.h>

#include "DruidsSageChatTypes.h"

#include "DruidsSageMessagingHandler.generated.h"

DECLARE_DELEGATE_OneParam(FMessageRequestSent, const FString);
DECLARE_DELEGATE_OneParam(FMessageRequestFailed, const FString);
DECLARE_DELEGATE_OneParam(FMessageContentUpdated, const FString);
DECLARE_DELEGATE_OneParam(FMessageResponseUpdated, const FDruidsSageChatResponse&);
DECLARE_DELEGATE(FMessageResponseCompleted);

UCLASS(NotBlueprintable, NotPlaceable, Category = "Implementation")
class SAGECORE_API UDruidsSageMessagingHandler : public UObject
{
	GENERATED_BODY()

public:
	explicit UDruidsSageMessagingHandler(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get());

	FMessageRequestSent OnMessageRequestSent;
	FMessageRequestFailed OnMessageRequestFailed;
	FMessageContentUpdated OnMessageContentUpdated;
	FMessageResponseUpdated OnMessageResponseUpdated;
	FMessageResponseCompleted OnMessageResponseCompleted;

	UFUNCTION()
	void RequestSent();

	UFUNCTION()
	void RequestFailed();

	UFUNCTION()
	void ProcessUpdated(const FDruidsSageChatResponse& Response);

	UFUNCTION()
	void ProcessCompleted(const FDruidsSageChatResponse& Response);

	UPROPERTY()
	class UScrollBox* ScrollBoxReference;

	void Destroy();

private:
	void ProcessResponse(const FDruidsSageChatResponse& Response);
};
